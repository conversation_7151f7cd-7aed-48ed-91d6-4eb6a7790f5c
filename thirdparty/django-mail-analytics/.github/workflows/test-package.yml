name: Test package

on:
  push:
  pull_request:
  workflow_dispatch:

jobs:

  prepare:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create matrix
        uses: fabiocaccamo/create-matrix-action@v5
        id: create_matrix
        with:
          matrix: |
            python-version {3.11}, django-version {5.0, 5.1}
            #            python-version {3.12}, django-version {5.0, 5.1}

    outputs:
      matrix: ${{ steps.create_matrix.outputs.matrix }}

  lint:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'
        cache: 'pip'

    - name: Install tools needed
      run: |
        sudo apt install gettext
        python -m pip install --upgrade pip
        pip install tox

    - name: Check migrations
      run: |
        tox -e migrations

  test:

    needs: prepare
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include: ${{from<PERSON><PERSON>(needs.prepare.outputs.matrix)}}

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Upgrade pip version
      run: |
        python -m pip install --upgrade pip

    - name: Install django
      run: |
        pip install "Django == ${{ matrix.django-version }}.*"

    - name: Install requirements
      run: |
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Run pre-commit
      run: |
        pre-commit run --all-files --show-diff-on-failure --verbose

    - name: Run tests
      run: |
        coverage run --append --source=django_mail_analytics runtests.py
        coverage report --show-missing
        coverage xml -o ./coverage.xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v5
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        fail_ci_if_error: false
        files: ./coverage.xml
        flags: unittests
        verbose: true
