name: Pre-commit auto-update

on:
  # every month
  schedule:
    - cron: "0 0 1 * *"
  # on demand
  workflow_dispatch:

jobs:
  auto-update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.x'
      - uses: browniebroke/pre-commit-autoupdate-action@main
      - uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          branch: update/pre-commit-hooks
          title: Update pre-commit hooks
          commit-message: "Update pre-commit hooks."
          body: Update versions of pre-commit hooks to latest version.
