name: Create release

on:
  push:
    tags:
      - '*.*.*'

jobs:
  build:
    runs-on: ubuntu-latest
    # environment: release
    permissions:
      id-token: write
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Extract release notes
        id: extract-release-notes
        uses: ffurrer2/extract-release-notes@v2

      - name: Create release
        uses: ncipollo/release-action@v1
        with:
          body: ${{ steps.extract-release-notes.outputs.release_notes }}
            #          token: ${{ secrets.WORKFLOWS_CREATE_RELEASE_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'
          cache: 'pip'

      - name: Build Package
        run: |
          pip install pip --upgrade
          pip install build
          python -m build

      - name: Publish on PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: dist/
          # password: ${{ secrets.WORKFLOWS_PUBLISH_TO_PYPI_TOKEN }}
