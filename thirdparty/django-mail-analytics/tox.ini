[tox]
envlist =
    py311-{dj50,dj51},
#    py312-{dj50,dj51},

[gh-actions]
python =
    3.11: py311
#    3.12: py312

[testenv]
passenv = CI,GITHUB_WORKFLOW

deps =
    dj50: Django == 5.0.*
    dj51: Django == 5.1.*
    -r requirements.txt
    -r requirements-test.txt

commands =
    pre-commit run -a
    coverage run --append --source=django_mail_analytics runtests.py
    coverage report --ignore-errors --show-missing

[testenv:migrations]
setenv =
    DJANGO_SETTINGS_MODULE=tests.settings
deps =
    -r requirements.txt
commands =
    python -m django makemigrations --check
