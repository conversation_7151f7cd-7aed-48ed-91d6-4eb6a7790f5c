# Django Mail Analytics Integration

This document explains how django-mail-analytics has been integrated with your campaign system for email open and click tracking.

## 🎯 What's Been Implemented

### 1. **Package Installation & Configuration**
- ✅ `django-mail-analytics` added to `INSTALLED_APPS`
- ✅ URL patterns configured: `re_path(r"^m/", include("django_mail_analytics.urls"))`
- ✅ Settings configured in `dcrm/settings.py`:
  ```python
  MAIL_ANALYTICS = {
      "DOMAIN": "localhost",
      "SCHEME": "http",
      "LENGTH": 6,
  }
  ```

### 2. **Email Sender Integration**
- ✅ Modified `campaign/email_sender.py` to add tracking keys to emails
- ✅ Both `send_campaign_email()` and `send_campaign_email_with_account()` now include tracking
- ✅ Tracking keys format: `CAMPAIGN_{campaign_id}_MSG_{message_assignment_id}@DMA`

### 3. **Analytics Service Integration**
- ✅ Created `campaign/mail_analytics_service.py` to handle tracking events
- ✅ Signal handlers automatically update campaign analytics when emails are opened/clicked
- ✅ Integrates with existing `AnalyticsService.handle_email_opened()` and `AnalyticsService.handle_email_clicked()`

### 4. **Admin Interface**
- ✅ Added django-mail-analytics models to Django admin
- ✅ Custom admin views for `Mail`, `MailRecipient`, and `MailRecipientAction`
- ✅ Easy viewing of tracking data and statistics

### 5. **Testing Tools**
- ✅ Test view at `/campaign/test-email-tracking/`
- ✅ Management command: `python manage.py test_mail_analytics`
- ✅ Test template with statistics and tracking links

## 🚀 How It Works

### Email Sending Process
1. **Campaign email is sent** via `send_campaign_email()` or `send_campaign_email_with_account()`
2. **Tracking key is added** to recipient list: `CAMPAIGN_123_MSG_456@DMA`
3. **django-mail-analytics automatically**:
   - Adds tracking pixel to HTML emails
   - Rewrites all links to go through tracking proxy
   - Creates `Mail` and `MailRecipient` objects

### Tracking Process
1. **Email is opened** → Tracking pixel is loaded → `MailRecipientAction` created (empty action)
2. **Link is clicked** → User goes through proxy → `MailRecipientAction` created (with URL)
3. **Signal handler** in `mail_analytics_service.py` processes the action
4. **Campaign analytics** are updated via existing `AnalyticsService`

## 📊 Testing the Integration

### Option 1: Management Command
```bash
python manage.py test_mail_analytics
```

### Option 2: Web Interface
1. Visit: `http://localhost:8000/campaign/test-email-tracking/`
2. Click "Send Test Email"
3. Check the statistics and tracking data
4. Click the tracking links to simulate opens/clicks

### Option 3: Django Admin
1. Go to Django Admin
2. Check "Mail Analytics" section
3. View `Mail`, `Mail Recipients`, and `Mail Recipient Actions`

## 🔗 Tracking URLs

When an email is sent, django-mail-analytics creates these tracking URLs:

### Open Tracking (Pixel)
```
http://localhost/m/i?q=ABC123
```
- Automatically added to HTML emails as invisible 1x1 pixel
- Triggered when email is opened in HTML-capable client

### Link Tracking (Proxy)
```
http://localhost/m/p?q=ABC123&u=https%3A%2F%2Fgoogle.com
```
- All links in HTML emails are rewritten to go through this proxy
- User is redirected to original URL after tracking

## 🎯 Campaign Integration

### Automatic Analytics Updates
When tracking events occur, the system automatically:

1. **Email Open**: Updates `MessageAssignment.opened = True` and calls `AnalyticsService.handle_email_opened()`
2. **Link Click**: Updates `MessageAssignment.clicked = True` and calls `AnalyticsService.handle_email_clicked()`

### Tracking Key Format
```
CAMPAIGN_{campaign_id}_MSG_{message_assignment_id}@DMA
```
- Example: `CAMPAIGN_123_MSG_456@DMA`
- Allows linking tracking events back to specific campaign and message assignment

## 📋 Next Steps

### 1. **Test the Integration**
```bash
# Run the test command
python manage.py test_mail_analytics

# Or visit the web interface
http://localhost:8000/campaign/test-email-tracking/
```

### 2. **Send Real Campaign Emails**
Your existing campaign sending process will now automatically include tracking:
- Email opens will be tracked
- Link clicks will be tracked
- Campaign analytics will be updated automatically

### 3. **Monitor Tracking Data**
- **Django Admin**: View detailed tracking data
- **Campaign Dashboard**: See updated open/click rates
- **Analytics API**: Access tracking data programmatically

### 4. **Production Configuration**
Update `MAIL_ANALYTICS` settings for production:
```python
MAIL_ANALYTICS = {
    "DOMAIN": "yourdomain.com",  # Your actual domain
    "SCHEME": "https",           # Use HTTPS in production
    "LENGTH": 8,                 # Longer tracking keys for security
}
```

## 🔧 Troubleshooting

### No Tracking Data
1. Check that emails have HTML content (tracking only works with HTML)
2. Verify `django_mail_analytics` is in `INSTALLED_APPS`
3. Ensure migrations have been run: `python manage.py migrate`
4. Check Django logs for errors

### Tracking URLs Not Working
1. Verify URL patterns are configured: `re_path(r"^m/", include("django_mail_analytics.urls"))`
2. Check `MAIL_ANALYTICS` settings
3. Ensure domain/scheme settings match your environment

### Analytics Not Updating
1. Check that signal handlers are registered (should happen automatically)
2. Verify `campaign.apps.CampaignConfig.ready()` is being called
3. Check logs for errors in `mail_analytics_service.py`

## 📚 Additional Resources

- **django-mail-analytics docs**: https://github.com/quertenmont/django-mail-analytics
- **Test command**: `python manage.py test_mail_analytics --help`
- **Test interface**: `/campaign/test-email-tracking/`
- **Admin interface**: `/admin/` → "Mail Analytics" section

## ✅ Summary

Your campaign system now has full email tracking capabilities:
- ✅ **Email opens** are automatically tracked
- ✅ **Link clicks** are automatically tracked  
- ✅ **Campaign analytics** are automatically updated
- ✅ **No changes needed** to existing campaign sending code
- ✅ **Full admin interface** for viewing tracking data
- ✅ **Testing tools** for verification

The integration is transparent to your existing workflow - just send campaigns as usual and tracking will happen automatically!
