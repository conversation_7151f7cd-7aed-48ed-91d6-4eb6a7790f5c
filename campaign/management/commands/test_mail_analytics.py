"""
Django management command to test django-mail-analytics integration
Usage: python manage.py test_mail_analytics
"""

from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django_mail_analytics.models import Mail, MailRecipient, MailRecipientAction
from campaign.models import Campaign, MessageAssignment
from campaign.mail_analytics_service import MailAnalyticsService
import time


class Command(BaseCommand):
    help = 'Test django-mail-analytics integration with campaign system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--send-test-email',
            action='store_true',
            help='Send a test email with tracking',
        )
        parser.add_argument(
            '--show-stats',
            action='store_true',
            help='Show current tracking statistics',
        )
        parser.add_argument(
            '--test-parsing',
            action='store_true',
            help='Test tracking key parsing',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🧪 Testing django-mail-analytics integration...\n')
        )

        if options['show_stats']:
            self.show_statistics()

        if options['test_parsing']:
            self.test_tracking_key_parsing()

        if options['send_test_email']:
            self.send_test_email()

        if not any([options['show_stats'], options['test_parsing'], options['send_test_email']]):
            # Run all tests by default
            self.show_statistics()
            self.test_tracking_key_parsing()
            self.send_test_email()

    def show_statistics(self):
        """Show current tracking statistics"""
        self.stdout.write(self.style.HTTP_INFO('📊 Current Tracking Statistics:'))
        
        mail_count = Mail.objects.count()
        recipient_count = MailRecipient.objects.count()
        action_count = MailRecipientAction.objects.count()
        
        self.stdout.write(f'   📧 Total emails tracked: {mail_count}')
        self.stdout.write(f'   📨 Total recipients: {recipient_count}')
        self.stdout.write(f'   🎯 Total actions (opens/clicks): {action_count}')
        
        if mail_count > 0:
            self.stdout.write('\n📬 Recent emails:')
            for mail in Mail.objects.order_by('-created_at')[:5]:
                actions = MailRecipientAction.objects.filter(mail_recipient__mail=mail).count()
                self.stdout.write(f'   📧 {mail.subject} - {mail.created_at} - {actions} actions')
        
        if action_count > 0:
            self.stdout.write('\n🎯 Recent actions:')
            for action in MailRecipientAction.objects.order_by('-created_at')[:5]:
                action_type = 'Open' if not action.action else f'Click: {action.action}'
                self.stdout.write(f'   🔍 {action_type} - {action.created_at}')
        
        self.stdout.write('')

    def test_tracking_key_parsing(self):
        """Test the tracking key parsing functionality"""
        self.stdout.write(self.style.HTTP_INFO('🔍 Testing tracking key parsing:'))
        
        test_keys = [
            'CAMPAIGN_123_MSG_456@DMA',
            'CAMPAIGN_1_MSG_999@DMA',
            'INVALID_KEY@DMA',
            'CAMPAIGN_ABC_MSG_456@DMA',  # Invalid - non-numeric IDs
            'TEST_CAMPAIGN_123@DMA',     # Different format
        ]
        
        for key in test_keys:
            result = MailAnalyticsService.get_message_assignment_from_tracking_key(key)
            if result:
                self.stdout.write(f'   ✅ {key} -> MessageAssignment {result.id}')
            else:
                self.stdout.write(f'   ❌ {key} -> No match found')
        
        self.stdout.write('')

    def send_test_email(self):
        """Send a test email with tracking"""
        self.stdout.write(self.style.HTTP_INFO('📧 Sending test email with tracking:'))
        
        try:
            # Get initial counts
            initial_mail_count = Mail.objects.count()
            initial_recipient_count = MailRecipient.objects.count()
            
            # Send test email
            result = send_mail(
                subject='Django Mail Analytics Test',
                message='This is a test email to verify tracking functionality.',
                from_email='<EMAIL>',
                recipient_list=['<EMAIL>', 'TEST_TRACKING_123@DMA'],
                html_message='''
                <html>
                <body>
                    <h1>Django Mail Analytics Test</h1>
                    <p>This is a test email to verify that django-mail-analytics is working correctly.</p>
                    <p><a href="https://google.com">Test Link 1</a></p>
                    <p><a href="https://github.com">Test Link 2</a></p>
                    <p>If tracking is working, you should see:</p>
                    <ul>
                        <li>A tracking pixel added to this email</li>
                        <li>Links rewritten with tracking URLs</li>
                        <li>New Mail and MailRecipient objects in the database</li>
                    </ul>
                    <p>Best regards,<br>Test System</p>
                </body>
                </html>
                ''',
                fail_silently=False
            )
            
            if result:
                self.stdout.write('   ✅ Test email sent successfully!')
                
                # Wait a moment for database operations
                time.sleep(1)
                
                # Check if tracking objects were created
                new_mail_count = Mail.objects.count()
                new_recipient_count = MailRecipient.objects.count()
                
                if new_mail_count > initial_mail_count:
                    self.stdout.write(f'   ✅ New Mail objects created: {new_mail_count - initial_mail_count}')
                    
                    # Get the latest mail
                    latest_mail = Mail.objects.latest('created_at')
                    self.stdout.write(f'   📧 Latest mail: {latest_mail.subject}')
                    self.stdout.write(f'   🔑 Tracking key: {latest_mail.key}')
                    
                    # Show tracking URLs
                    self.stdout.write('   🔗 Tracking URLs:')
                    self.stdout.write(f'      📊 Open tracking: http://localhost/m/i?q={latest_mail.key}')
                    self.stdout.write(f'      🔗 Link tracking: http://localhost/m/p?q={latest_mail.key}&u=https%3A%2F%2Fgoogle.com')
                    
                    # Show recipients
                    recipients = latest_mail.recipients.all()
                    self.stdout.write(f'   📨 Recipients ({recipients.count()}):')
                    for recipient in recipients:
                        self.stdout.write(f'      📧 {recipient.email}')
                else:
                    self.stdout.write('   ⚠️ No new Mail objects created - tracking may not be working')
                
                if new_recipient_count > initial_recipient_count:
                    self.stdout.write(f'   ✅ New MailRecipient objects created: {new_recipient_count - initial_recipient_count}')
                else:
                    self.stdout.write('   ⚠️ No new MailRecipient objects created')
                    
            else:
                self.stdout.write('   ❌ Failed to send test email')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   💥 Error sending test email: {str(e)}')
            )
        
        self.stdout.write('')

    def handle_success(self):
        """Show success message and next steps"""
        self.stdout.write(
            self.style.SUCCESS('✅ Django Mail Analytics integration test completed!')
        )
        self.stdout.write('\n📋 Next steps:')
        self.stdout.write('   1. Visit the Django admin to see tracking data')
        self.stdout.write('   2. Visit /campaign/test-email-tracking/ to test in browser')
        self.stdout.write('   3. Click tracking URLs to simulate opens and clicks')
        self.stdout.write('   4. Check campaign analytics integration')
        self.stdout.write('')
