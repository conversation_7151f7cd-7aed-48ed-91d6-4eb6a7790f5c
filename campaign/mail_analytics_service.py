"""
Service to integrate django-mail-analytics with campaign tracking system.
This service handles email open and click tracking events from django-mail-analytics
and updates the campaign analytics accordingly.
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django_mail_analytics.models import MailRecipientAction
from .models import MessageAssignment, Campaign
from .services import AnalyticsService

logger = logging.getLogger(__name__)


class MailAnalyticsService:
    """
    Service to handle django-mail-analytics events and integrate with campaign analytics
    """
    
    @staticmethod
    def get_message_assignment_from_tracking_key(tracking_key):
        """
        Extract MessageAssignment from django-mail-analytics tracking key
        
        Args:
            tracking_key: String like "CAMPAIGN_123_MSG_456@DMA"
            
        Returns:
            MessageAssignment object or None
        """
        try:
            if not tracking_key or '@DMA' not in tracking_key:
                return None
                
            # Parse tracking key: "CAMPAIGN_123_MSG_456@DMA"
            key_part = tracking_key.split('@DMA')[0]
            parts = key_part.split('_')
            
            if len(parts) >= 4 and parts[0] == 'CAMPAIGN' and parts[2] == 'MSG':
                campaign_id = int(parts[1])
                message_assignment_id = int(parts[3])
                
                # Get the message assignment
                message_assignment = MessageAssignment.objects.select_related(
                    'campaign', 'campaign_lead', 'message'
                ).get(
                    id=message_assignment_id,
                    campaign_id=campaign_id
                )
                
                return message_assignment
                
        except (ValueError, MessageAssignment.DoesNotExist, IndexError) as e:
            logger.warning(f"Could not parse tracking key '{tracking_key}': {str(e)}")
            
        return None
    
    @staticmethod
    def handle_email_opened(mail_recipient_action):
        """
        Handle email open event from django-mail-analytics
        
        Args:
            mail_recipient_action: MailRecipientAction object with empty action (email open)
        """
        try:
            # Get the Mail object to find the tracking key
            mail = mail_recipient_action.mail_recipient.mail
            
            # Look for tracking key in recipients
            tracking_key = None
            for recipient in mail.recipients.all():
                if '@DMA' in recipient.email:
                    tracking_key = recipient.email
                    break
            
            if not tracking_key:
                logger.warning(f"No tracking key found for mail {mail.id}")
                return
            
            # Get message assignment from tracking key
            message_assignment = MailAnalyticsService.get_message_assignment_from_tracking_key(tracking_key)
            if not message_assignment:
                logger.warning(f"Could not find message assignment for tracking key: {tracking_key}")
                return
            
            # Update message assignment opened status
            if not message_assignment.opened:
                message_assignment.opened = True
                message_assignment.opened_at = mail_recipient_action.created_at
                message_assignment.save(update_fields=['opened', 'opened_at'])
                
                # Update campaign analytics
                AnalyticsService.handle_email_opened(message_assignment)
                
                logger.info(f"Email opened tracked for message assignment {message_assignment.id}")
            
        except Exception as e:
            logger.error(f"Error handling email opened event: {str(e)}")
    
    @staticmethod
    def handle_link_clicked(mail_recipient_action):
        """
        Handle link click event from django-mail-analytics
        
        Args:
            mail_recipient_action: MailRecipientAction object with action containing clicked URL
        """
        try:
            # Get the Mail object to find the tracking key
            mail = mail_recipient_action.mail_recipient.mail
            
            # Look for tracking key in recipients
            tracking_key = None
            for recipient in mail.recipients.all():
                if '@DMA' in recipient.email:
                    tracking_key = recipient.email
                    break
            
            if not tracking_key:
                logger.warning(f"No tracking key found for mail {mail.id}")
                return
            
            # Get message assignment from tracking key
            message_assignment = MailAnalyticsService.get_message_assignment_from_tracking_key(tracking_key)
            if not message_assignment:
                logger.warning(f"Could not find message assignment for tracking key: {tracking_key}")
                return
            
            # Update message assignment clicked status
            if not message_assignment.clicked:
                message_assignment.clicked = True
                message_assignment.clicked_at = mail_recipient_action.created_at
                message_assignment.save(update_fields=['clicked', 'clicked_at'])
                
                # Update campaign analytics
                AnalyticsService.handle_email_clicked(message_assignment)
                
                logger.info(f"Link clicked tracked for message assignment {message_assignment.id}, URL: {mail_recipient_action.action}")
            
        except Exception as e:
            logger.error(f"Error handling link clicked event: {str(e)}")


@receiver(post_save, sender=MailRecipientAction)
def handle_mail_recipient_action(sender, instance, created, **kwargs):
    """
    Signal handler for MailRecipientAction creation.
    This is triggered when django-mail-analytics records an email open or link click.
    """
    if not created:
        return
    
    try:
        if not instance.action:
            # Empty action means email was opened
            MailAnalyticsService.handle_email_opened(instance)
        else:
            # Non-empty action means a link was clicked
            MailAnalyticsService.handle_link_clicked(instance)
            
    except Exception as e:
        logger.error(f"Error in mail recipient action signal handler: {str(e)}")
