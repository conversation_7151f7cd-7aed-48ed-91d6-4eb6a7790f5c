"""
Service to integrate django-mail-analytics with campaign tracking system.
This service handles email open and click tracking events from django-mail-analytics
and updates the campaign analytics accordingly.
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django_mail_analytics.models import MailRecipientAction
from .models import MessageAssignment, Campaign
from .services import AnalyticsService

logger = logging.getLogger(__name__)


class MailAnalyticsService:
    """
    Service to handle django-mail-analytics events and integrate with campaign analytics
    """
    
    @staticmethod
    def get_message_assignment_from_mail(mail):
        """
        Extract MessageAssignment from django-mail-analytics Mail object
        We'll try multiple approaches to find the message assignment

        Args:
            mail: Mail object from django-mail-analytics

        Returns:
            MessageAssignment object or None
        """
        try:
            # Approach 1: Try to find campaign/message info in the mail subject or key
            # django-mail-analytics creates a key based on subject and date

            # Look for campaign emails by checking if any MessageAssignment matches
            # the subject and approximate time
            from django.utils import timezone
            from datetime import timedelta

            # Search for message assignments sent around the same time with same subject
            time_window = timedelta(minutes=5)  # 5 minute window
            start_time = mail.created_at - time_window
            end_time = mail.created_at + time_window

            # Try to find matching message assignment by subject and time
            message_assignments = MessageAssignment.objects.filter(
                message__subject=mail.subject,
                sent_at__range=(start_time, end_time),
                sent=True
            ).select_related('campaign', 'campaign_lead', 'message')

            # If we find exactly one match, that's likely our message assignment
            if message_assignments.count() == 1:
                return message_assignments.first()

            # If multiple matches, try to narrow down by recipient email
            recipients = mail.recipients.all()
            if recipients.exists():
                recipient_emails = [r.email for r in recipients if '@DMA' not in r.email]
                if recipient_emails:
                    recipient_email = recipient_emails[0]

                    # Find message assignment for this specific recipient
                    for ma in message_assignments:
                        if ma.campaign_lead.lead.email == recipient_email:
                            return ma

            logger.warning(f"Could not find unique message assignment for mail '{mail.subject}' at {mail.created_at}")

        except Exception as e:
            logger.warning(f"Error finding message assignment for mail {mail.id}: {str(e)}")

        return None
    
    @staticmethod
    def handle_email_opened(mail_recipient_action):
        """
        Handle email open event from django-mail-analytics
        
        Args:
            mail_recipient_action: MailRecipientAction object with empty action (email open)
        """
        try:
            # Get the Mail object
            mail = mail_recipient_action.mail_recipient.mail

            # Get message assignment from mail
            message_assignment = MailAnalyticsService.get_message_assignment_from_mail(mail)
            if not message_assignment:
                logger.warning(f"Could not find message assignment for mail: {mail.subject}")
                return
            
            # Update message assignment opened status
            if not message_assignment.opened:
                message_assignment.opened = True
                message_assignment.opened_at = mail_recipient_action.created_at
                message_assignment.save(update_fields=['opened', 'opened_at'])
                
                # Update campaign analytics
                AnalyticsService.handle_email_opened(message_assignment)
                
                logger.info(f"Email opened tracked for message assignment {message_assignment.id}")
            
        except Exception as e:
            logger.error(f"Error handling email opened event: {str(e)}")
    
    @staticmethod
    def handle_link_clicked(mail_recipient_action):
        """
        Handle link click event from django-mail-analytics
        
        Args:
            mail_recipient_action: MailRecipientAction object with action containing clicked URL
        """
        try:
            # Get the Mail object
            mail = mail_recipient_action.mail_recipient.mail

            # Get message assignment from mail
            message_assignment = MailAnalyticsService.get_message_assignment_from_mail(mail)
            if not message_assignment:
                logger.warning(f"Could not find message assignment for mail: {mail.subject}")
                return
            
            # Update message assignment clicked status
            if not message_assignment.clicked:
                message_assignment.clicked = True
                message_assignment.clicked_at = mail_recipient_action.created_at
                message_assignment.save(update_fields=['clicked', 'clicked_at'])
                
                # Update campaign analytics
                AnalyticsService.handle_email_clicked(message_assignment)
                
                logger.info(f"Link clicked tracked for message assignment {message_assignment.id}, URL: {mail_recipient_action.action}")
            
        except Exception as e:
            logger.error(f"Error handling link clicked event: {str(e)}")


@receiver(post_save, sender=MailRecipientAction)
def handle_mail_recipient_action(sender, instance, created, **kwargs):
    """
    Signal handler for MailRecipientAction creation.
    This is triggered when django-mail-analytics records an email open or link click.
    """
    if not created:
        return
    
    try:
        if not instance.action:
            # Empty action means email was opened
            MailAnalyticsService.handle_email_opened(instance)
        else:
            # Non-empty action means a link was clicked
            MailAnalyticsService.handle_link_clicked(instance)
            
    except Exception as e:
        logger.error(f"Error in mail recipient action signal handler: {str(e)}")
