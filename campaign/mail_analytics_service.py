"""
Service to integrate django-mail-analytics with campaign tracking system.
This service handles email open and click tracking events from django-mail-analytics
and updates the campaign analytics accordingly.
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django_mail_analytics.models import MailRecipientAction
from .models import MessageAssignment, Campaign
from .services import AnalyticsService

logger = logging.getLogger(__name__)


class MailAnalyticsService:
    """
    Service to handle django-mail-analytics events and integrate with campaign analytics
    """
    
    @staticmethod
    def get_message_assignment_from_mail(mail):
        """
        Extract MessageAssignment from django-mail-analytics Mail object
        Uses the mail.key which contains our tracking information

        Args:
            mail: Mail object from django-mail-analytics

        Returns:
            MessageAssignment object or None
        """
        try:
            # The mail.key contains our tracking key: "CAMPAIGN_123_MSG_456"
            # django-mail-analytics uses this as the key when we send with @DMA tracker

            key = mail.key
            if not key:
                logger.warning(f"No key found for mail {mail.id}")
                return None

            # Try to parse the key: "CAMPAIGN_123_MSG_456"
            if key.startswith('CAMPAIGN_') and '_MSG_' in key:
                try:
                    parts = key.split('_')
                    if len(parts) >= 4 and parts[0] == 'CAMPAIGN' and parts[2] == 'MSG':
                        campaign_id = int(parts[1])
                        message_assignment_id = int(parts[3])

                        # Get the message assignment
                        message_assignment = MessageAssignment.objects.select_related(
                            'campaign', 'campaign_lead', 'message'
                        ).get(
                            id=message_assignment_id,
                            campaign_id=campaign_id
                        )

                        return message_assignment

                except (ValueError, MessageAssignment.DoesNotExist, IndexError) as e:
                    logger.warning(f"Could not parse tracking key '{key}': {str(e)}")

            # Fallback: try to match by subject and recipient
            logger.info(f"Trying fallback matching for mail key: {key}")

            # Get recipient email (excluding @DMA trackers)
            recipients = mail.recipients.all()
            recipient_emails = []
            for recipient in recipients:
                # Parse recipient field which might be "email1,email2,..."
                emails = recipient.recipient.split(',')
                for email in emails:
                    email = email.strip()
                    if email and not email.endswith('@DMA'):
                        recipient_emails.append(email)

            if not recipient_emails:
                logger.warning(f"No valid recipient emails found for mail {mail.id}")
                return None

            recipient_email = recipient_emails[0]

            # Search for message assignments with matching subject and recipient
            from django.utils import timezone
            from datetime import timedelta

            time_window = timedelta(minutes=10)  # 10 minute window
            start_time = mail.created_at - time_window
            end_time = mail.created_at + time_window

            message_assignments = MessageAssignment.objects.filter(
                message__subject=mail.subject,
                campaign_lead__lead__email=recipient_email,
                sent_at__range=(start_time, end_time),
                sent=True
            ).select_related('campaign', 'campaign_lead', 'message')

            if message_assignments.count() == 1:
                return message_assignments.first()
            elif message_assignments.count() > 1:
                # Return the most recent one
                return message_assignments.order_by('-sent_at').first()

            logger.warning(f"Could not find message assignment for mail '{mail.subject}' to {recipient_email}")

        except Exception as e:
            logger.error(f"Error finding message assignment for mail {mail.id}: {str(e)}")

        return None
    
    @staticmethod
    def handle_email_opened(mail_recipient_action):
        """
        Handle email open event from django-mail-analytics
        
        Args:
            mail_recipient_action: MailRecipientAction object with empty action (email open)
        """
        try:
            # Get the Mail object
            mail = mail_recipient_action.mail_recipient.mail

            # Get message assignment from mail
            message_assignment = MailAnalyticsService.get_message_assignment_from_mail(mail)
            if not message_assignment:
                logger.warning(f"Could not find message assignment for mail: {mail.subject}")
                return
            
            # Update message assignment opened status
            if not message_assignment.opened:
                message_assignment.opened = True
                message_assignment.opened_at = mail_recipient_action.created_at
                message_assignment.save(update_fields=['opened', 'opened_at'])
                
                # Update campaign analytics
                AnalyticsService.handle_email_opened(message_assignment)
                
                logger.info(f"Email opened tracked for message assignment {message_assignment.id}")
            
        except Exception as e:
            logger.error(f"Error handling email opened event: {str(e)}")
    
    @staticmethod
    def handle_link_clicked(mail_recipient_action):
        """
        Handle link click event from django-mail-analytics
        
        Args:
            mail_recipient_action: MailRecipientAction object with action containing clicked URL
        """
        try:
            # Get the Mail object
            mail = mail_recipient_action.mail_recipient.mail

            # Get message assignment from mail
            message_assignment = MailAnalyticsService.get_message_assignment_from_mail(mail)
            if not message_assignment:
                logger.warning(f"Could not find message assignment for mail: {mail.subject}")
                return
            
            # Update message assignment clicked status
            if not message_assignment.clicked:
                message_assignment.clicked = True
                message_assignment.clicked_at = mail_recipient_action.created_at
                message_assignment.save(update_fields=['clicked', 'clicked_at'])
                
                # Update campaign analytics
                AnalyticsService.handle_email_clicked(message_assignment)
                
                logger.info(f"Link clicked tracked for message assignment {message_assignment.id}, URL: {mail_recipient_action.action}")
            
        except Exception as e:
            logger.error(f"Error handling link clicked event: {str(e)}")


@receiver(post_save, sender=MailRecipientAction)
def handle_mail_recipient_action(sender, instance, created, **kwargs):
    """
    Signal handler for MailRecipientAction creation.
    This is triggered when django-mail-analytics records an email open or link click.
    """
    if not created:
        return
    
    try:
        if not instance.action:
            # Empty action means email was opened
            MailAnalyticsService.handle_email_opened(instance)
        else:
            # Non-empty action means a link was clicked
            MailAnalyticsService.handle_link_clicked(instance)
            
    except Exception as e:
        logger.error(f"Error in mail recipient action signal handler: {str(e)}")
