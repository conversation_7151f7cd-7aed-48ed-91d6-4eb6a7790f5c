{% extends "base-app.html" %}

{% block title %}Test Email Tracking{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">📧 Test Email Tracking</h1>
        
        <!-- Test Email Form -->
        <div class="card bg-base-100 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">🧪 Send Test Email</h2>
                <p class="text-base-content/70 mb-4">
                    Send a test email to verify that django-mail-analytics is working correctly.
                    The email will include tracking pixels and link rewriting.
                </p>
                
                {% if success_message %}
                    <div class="alert alert-success mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{{ success_message }}</span>
                    </div>
                {% endif %}
                
                {% if error_message %}
                    <div class="alert alert-error mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{{ error_message }}</span>
                    </div>
                {% endif %}
                
                <form method="post" class="card-actions">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-primary">
                        📧 Send Test Email
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Tracking Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="stat bg-base-200 rounded-lg">
                <div class="stat-figure text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="stat-title">Total Emails</div>
                <div class="stat-value text-primary">{{ mail_count }}</div>
                <div class="stat-desc">Tracked emails sent</div>
            </div>
            
            <div class="stat bg-base-200 rounded-lg">
                <div class="stat-figure text-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="stat-title">Recipients</div>
                <div class="stat-value text-secondary">{{ recipient_count }}</div>
                <div class="stat-desc">Email recipients</div>
            </div>
            
            <div class="stat bg-base-200 rounded-lg">
                <div class="stat-figure text-accent">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <div class="stat-title">Actions</div>
                <div class="stat-value text-accent">{{ action_count }}</div>
                <div class="stat-desc">Opens & clicks</div>
            </div>
        </div>
        
        <!-- Recent Emails -->
        {% if recent_mails %}
        <div class="card bg-base-100 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">📬 Recent Emails</h2>
                <div class="overflow-x-auto">
                    <table class="table table-zebra">
                        <thead>
                            <tr>
                                <th>Subject</th>
                                <th>Tracking Key</th>
                                <th>Sent At</th>
                                <th>Recipients</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mail in recent_mails %}
                            <tr>
                                <td>{{ mail.subject }}</td>
                                <td><code class="text-xs">{{ mail.key }}</code></td>
                                <td>{{ mail.created_at|date:"M d, Y H:i" }}</td>
                                <td>{{ mail.recipients.count }}</td>
                                <td>
                                    <div class="flex gap-2">
                                        <a href="http://localhost/m/i?q={{ mail.key }}" target="_blank" class="btn btn-xs btn-outline">
                                            📊 Open Pixel
                                        </a>
                                        <a href="http://localhost/m/p?q={{ mail.key }}&u=https%3A%2F%2Fgoogle.com" target="_blank" class="btn btn-xs btn-outline">
                                            🔗 Test Link
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Recent Actions -->
        {% if recent_actions %}
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">🎯 Recent Actions</h2>
                <div class="overflow-x-auto">
                    <table class="table table-zebra">
                        <thead>
                            <tr>
                                <th>Email Subject</th>
                                <th>Recipient</th>
                                <th>Action Type</th>
                                <th>Action Details</th>
                                <th>Timestamp</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for action in recent_actions %}
                            <tr>
                                <td>{{ action.mail_recipient.mail.subject }}</td>
                                <td>{{ action.mail_recipient.email }}</td>
                                <td>
                                    {% if action.action %}
                                        <span class="badge badge-primary">🔗 Link Click</span>
                                    {% else %}
                                        <span class="badge badge-secondary">📊 Email Open</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if action.action %}
                                        <code class="text-xs">{{ action.action|truncatechars:50 }}</code>
                                    {% else %}
                                        <span class="text-base-content/50">Email opened</span>
                                    {% endif %}
                                </td>
                                <td>{{ action.created_at|date:"M d, Y H:i:s" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Instructions -->
        <div class="card bg-base-200 shadow-xl mt-8">
            <div class="card-body">
                <h2 class="card-title">📋 How to Test</h2>
                <div class="space-y-4">
                    <div class="steps steps-vertical lg:steps-horizontal">
                        <div class="step step-primary">Send test email</div>
                        <div class="step step-primary">Check email in browser</div>
                        <div class="step step-primary">Click tracking links</div>
                        <div class="step step-primary">View results here</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="font-bold">Testing Tips:</h3>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li>Click the "Open Pixel" link to simulate email opening</li>
                                <li>Click the "Test Link" to simulate link clicking</li>
                                <li>Refresh this page to see updated statistics</li>
                                <li>Check the Django admin for detailed tracking data</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
