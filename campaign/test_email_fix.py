"""
Quick test to verify the email sending fix
Run this in Django shell: python manage.py shell
Then: exec(open('campaign/test_email_fix.py').read())
"""

from django.core.mail import EmailMultiAlternatives
from django.core.mail.backends.console import EmailBackend
import logging

logger = logging.getLogger(__name__)

def test_email_with_tracking():
    """Test email sending with tracking key to verify no tuple errors"""
    
    print("🧪 Testing email sending with tracking key...")
    
    try:
        # Simulate the fixed approach
        recipient_email = "<EMAIL>"
        tracking_key = "CAMPAIGN_123_MSG_456@DMA"
        recipients = [recipient_email, tracking_key]
        
        # Use console backend for testing (won't actually send)
        backend = EmailBackend()
        
        email = EmailMultiAlternatives(
            subject="Test Email with Tracking Fix",
            body="This is a test email to verify the tracking fix.",
            from_email="<EMAIL>",
            to=recipients,
            connection=backend
        )
        
        # Add HTML version
        html_content = """
        <html>
        <body>
            <h1>Test Email</h1>
            <p>This is a test email with tracking.</p>
            <p><a href="https://google.com">Test Link</a></p>
        </body>
        </html>
        """
        email.attach_alternative(html_content, "text/html")
        
        # Try to send
        result = email.send(fail_silently=False)
        
        if result:
            print("✅ Email sending test passed!")
            print(f"   📧 Recipients: {recipients}")
            print(f"   📊 Tracking key: {tracking_key}")
            print("   🎯 No tuple errors occurred")
        else:
            print("❌ Email sending test failed")
            
    except Exception as e:
        print(f"💥 Error in email test: {str(e)}")
        print(f"💥 Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

def test_recipient_list_formats():
    """Test different recipient list formats"""
    
    print("\n🔍 Testing different recipient list formats...")
    
    test_cases = [
        # Case 1: Simple emails only
        ["<EMAIL>", "TRACKING@DMA"],
        
        # Case 2: Mixed format (this was causing the error)
        ["Test User <<EMAIL>>", "TRACKING@DMA"],
        
        # Case 3: Multiple recipients with tracking
        ["<EMAIL>", "<EMAIL>", "TRACKING@DMA"],
    ]
    
    for i, recipients in enumerate(test_cases, 1):
        try:
            print(f"\n   Test Case {i}: {recipients}")
            
            backend = EmailBackend()
            email = EmailMultiAlternatives(
                subject=f"Test Case {i}",
                body="Test email",
                from_email="<EMAIL>",
                to=recipients,
                connection=backend
            )
            
            # This is where the error would occur
            result = email.send(fail_silently=False)
            print(f"   ✅ Case {i} passed")
            
        except Exception as e:
            print(f"   ❌ Case {i} failed: {str(e)}")

if __name__ == "__main__":
    test_email_with_tracking()
    test_recipient_list_formats()
    print("\n🎯 Test completed!")
