"""
Test script to verify django-mail-analytics integration with campaign system
"""

from django.core.mail import send_mail
from django.test import TestCase
from django.contrib.auth import get_user_model
from clients.models import SubscribedCompany, EmailAccount
from campaign.models import Campaign, Message, Lead, CampaignLead, MessageAssignment, Product
from django_mail_analytics.models import Mail, MailRecipient, MailRecipientAction
import logging

logger = logging.getLogger(__name__)

def test_email_tracking_integration():
    """
    Test function to verify django-mail-analytics integration
    Run this in Django shell: python manage.py shell
    Then: from campaign.test_mail_analytics import test_email_tracking_integration; test_email_tracking_integration()
    """
    
    print("🧪 Testing django-mail-analytics integration...")
    
    try:
        # Test 1: Send a simple email with tracking
        print("\n📧 Test 1: Sending test email with tracking...")
        
        result = send_mail(
            subject="Test Email with Tracking",
            message="This is a plain text test message.",
            from_email="<EMAIL>",
            recipient_list=["<EMAIL>", "TEST_CAMPAIGN_123@DMA"],
            html_message="""
            <html>
            <body>
                <h1>Test Email</h1>
                <p>This is a test email with tracking.</p>
                <p><a href="https://google.com">Click this link</a></p>
                <p>Best regards,<br>Test Team</p>
            </body>
            </html>
            """,
            fail_silently=False
        )
        
        if result:
            print("✅ Email sent successfully!")
            
            # Check if Mail object was created
            mail_count = Mail.objects.count()
            print(f"📊 Total Mail objects in database: {mail_count}")
            
            if mail_count > 0:
                latest_mail = Mail.objects.latest('created_at')
                print(f"📧 Latest mail: {latest_mail.subject}")
                print(f"📧 Mail key: {latest_mail.key}")
                print(f"📧 Recipients count: {latest_mail.recipients.count()}")
                
                # Show recipients
                for recipient in latest_mail.recipients.all():
                    print(f"   📨 Recipient: {recipient.email}")
                
                # Show tracking URLs that would be generated
                print(f"\n🔗 Tracking URLs:")
                print(f"   📊 Open tracking: http://localhost/m/i?q={latest_mail.key}")
                print(f"   🔗 Link proxy: http://localhost/m/p?q={latest_mail.key}&u=https%3A%2F%2Fgoogle.com")
                
            else:
                print("⚠️ No Mail objects found - tracking may not be working")
        else:
            print("❌ Email sending failed")
            
    except Exception as e:
        print(f"💥 Error in test: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Check django-mail-analytics models
    print(f"\n📊 Django Mail Analytics Database Status:")
    print(f"   📧 Mail objects: {Mail.objects.count()}")
    print(f"   📨 MailRecipient objects: {MailRecipient.objects.count()}")
    print(f"   📊 MailRecipientAction objects: {MailRecipientAction.objects.count()}")
    
    # Test 3: Show recent mail analytics data
    if Mail.objects.exists():
        print(f"\n📧 Recent emails:")
        for mail in Mail.objects.order_by('-created_at')[:5]:
            print(f"   📧 {mail.subject} - {mail.created_at} - Key: {mail.key}")
            actions = MailRecipientAction.objects.filter(mail_recipient__mail=mail)
            print(f"      📊 Actions: {actions.count()}")
            for action in actions[:3]:
                action_type = "Open" if not action.action else f"Click: {action.action}"
                print(f"         🔍 {action_type} - {action.created_at}")


def simulate_campaign_email_tracking():
    """
    Simulate a campaign email with tracking
    """
    print("\n🎯 Simulating campaign email tracking...")
    
    try:
        # This simulates what happens when your campaign sends an email
        campaign_id = 1  # Replace with actual campaign ID
        message_assignment_id = 1  # Replace with actual message assignment ID
        
        tracking_key = f"CAMPAIGN_{campaign_id}_MSG_{message_assignment_id}@DMA"
        
        result = send_mail(
            subject="Campaign Email Test",
            message="This is a campaign email test.",
            from_email="<EMAIL>",
            recipient_list=["<EMAIL>", tracking_key],
            html_message="""
            <html>
            <body>
                <h1>Hello from Campaign!</h1>
                <p>This is a personalized campaign email.</p>
                <p><a href="https://yourwebsite.com/landing">Visit our website</a></p>
                <p>Best regards,<br>Campaign Team</p>
            </body>
            </html>
            """,
            fail_silently=False
        )
        
        if result:
            print("✅ Campaign email sent successfully!")
            print(f"🔑 Tracking key used: {tracking_key}")
            
            # Find the mail object
            mail = Mail.objects.filter(key__icontains=tracking_key.split('@')[0]).first()
            if mail:
                print(f"📧 Mail object found: {mail.subject}")
                print(f"🔗 Open tracking URL: http://localhost/m/i?q={mail.key}")
            else:
                print("⚠️ Mail object not found")
        else:
            print("❌ Campaign email sending failed")
            
    except Exception as e:
        print(f"💥 Error in campaign simulation: {str(e)}")


if __name__ == "__main__":
    test_email_tracking_integration()
    simulate_campaign_email_tracking()
